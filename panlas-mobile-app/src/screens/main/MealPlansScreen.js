import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { mealPlansAPI, userAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';
import FloatingChatButton from '../../components/FloatingChatButton';

const MealPlansScreen = ({ navigation }) => {
  const [mealPlans, setMealPlans] = useState([]);
  const [savedPlans, setSavedPlans] = useState([]);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [userDietaryPreferences, setUserDietaryPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState(getCurrentWeek());
  const [activeTab, setActiveTab] = useState('weekly');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [numberOfDays, setNumberOfDays] = useState(7);
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Delete functionality state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDateForDelete, setSelectedDateForDelete] = useState(null);
  const [deletingDate, setDeletingDate] = useState(null);
  const [showUndoSnackbar, setShowUndoSnackbar] = useState(false);
  const [deletedMealPlan, setDeletedMealPlan] = useState(null);

  const { user } = useAuth();
  const {
    favoriteMealPlans,
    addFavoriteMealPlan,
    removeFavoriteMealPlan,
    isFavoriteMealPlan
  } = useFavorites();

  useEffect(() => {
    loadMealPlans();
    loadFamilyData();
  }, [selectedWeek]);

  const loadFamilyData = async () => {
    try {
      const [familyResponse, preferencesResponse] = await Promise.all([
        userAPI.getFamilyMembers(),
        userAPI.getDietaryPreferences(),
      ]);

      // Ensure we always set an array for familyMembers
      const familyData = familyResponse?.data;
      setFamilyMembers(Array.isArray(familyData) ? familyData : []);

      // Ensure we always set an object for preferences
      setUserDietaryPreferences(preferencesResponse?.data || {});

      console.log('Family data loaded successfully:', {
        familyMembers: Array.isArray(familyData) ? familyData.length : 0,
        preferences: preferencesResponse?.data
      });
    } catch (error) {
      console.error('Error loading family data:', error);
      // Set safe defaults on error
      setFamilyMembers([]);
      setUserDietaryPreferences({});
    }
  };

  function getCurrentWeek() {
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return {
      start: startOfWeek.toISOString().split('T')[0],
      end: endOfWeek.toISOString().split('T')[0],
    };
  }

  const loadMealPlans = async () => {
    try {
      setLoading(true);

      // Use the exact same API call as the website
      const response = await mealPlansAPI.getMealPlans();
      const plansData = response.data || [];

      console.log('Raw meal plans data:', plansData);

      // Use the exact same mapping as the website
      // The website expects meal plans to have a 'meals' array with meal objects
      setMealPlans(plansData);

      // Also try to load saved plans
      try {
        const savedResponse = await mealPlansAPI.getSavedMealPlans();
        setSavedPlans(savedResponse.data || []);
      } catch (savedError) {
        console.log('No saved plans available');
        setSavedPlans([]);
      }

    } catch (error) {
      console.error('Error loading meal plans:', error);
      Alert.alert('Error', 'Failed to load meal plans');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadMealPlans(), loadFamilyData()]);
    setRefreshing(false);
  };

  const getPreferencesButtonText = () => {
    try {
      // Return early if data is not loaded yet
      if (!userDietaryPreferences || !Array.isArray(familyMembers)) {
        return 'Set Preferences';
      }

      const hasUserPreferences = userDietaryPreferences?.restrictions?.length > 0 ||
                                userDietaryPreferences?.allergies?.length > 0;

      // Safely check family preferences
      const hasFamilyPreferences = familyMembers.length > 0 && familyMembers.some(member =>
        member?.dietaryPreferences?.restrictions?.length > 0 ||
        member?.dietaryPreferences?.allergies?.length > 0
      );

      if (hasUserPreferences || hasFamilyPreferences) {
        return 'Update Preferences';
      }
      return 'Set Preferences';
    } catch (error) {
      console.error('Error in getPreferencesButtonText:', error);
      return 'Set Preferences';
    }
  };

  const handleSetPreferences = () => {
    // Navigate to the Family screen within the Profile stack
    // Use getParent() to access the tab navigator
    const tabNavigator = navigation.getParent();
    if (tabNavigator) {
      tabNavigator.navigate('Profile', {
        screen: 'Family'
      });
    } else {
      // Fallback: navigate directly to Profile tab first
      navigation.navigate('Profile');
    }
  };

  const navigateWeek = (direction) => {
    const currentStart = new Date(selectedWeek.start);
    const newStart = new Date(currentStart.setDate(currentStart.getDate() + (direction * 7)));
    const newEnd = new Date(newStart.getTime() + (6 * 24 * 60 * 60 * 1000));

    setSelectedWeek({
      start: newStart.toISOString().split('T')[0],
      end: newEnd.toISOString().split('T')[0],
    });
  };

  const getDaysOfWeek = () => {
    const days = [];
    const start = new Date(selectedWeek.start);

    for (let i = 0; i < 7; i++) {
      const day = new Date(start.getTime() + (i * 24 * 60 * 60 * 1000));
      days.push({
        date: day.toISOString().split('T')[0],
        dayName: day.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: day.getDate(),
        isToday: day.toDateString() === new Date().toDateString(),
      });
    }

    return days;
  };

  // Helper function to normalize dates to YYYY-MM-DD format
  const normalizeDateString = (dateInput) => {
    if (!dateInput || dateInput === 'null' || dateInput === '') return null;

    try {
      // If it's already in YYYY-MM-DD format, return as is
      if (typeof dateInput === 'string' && dateInput.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateInput;
      }

      // If it's an ISO string, extract the date part
      if (typeof dateInput === 'string' && dateInput.match(/^\d{4}-\d{2}-\d{2}T/)) {
        return dateInput.split('T')[0];
      }

      // Handle Date objects or other date strings
      const dateObj = new Date(dateInput);
      if (isNaN(dateObj.getTime())) {
        return null;
      }

      // Convert to YYYY-MM-DD format using local date
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('Error normalizing date:', dateInput, error);
      return null;
    }
  };

  const getMealPlanForDate = (date) => {
    // Find the meal plan for this specific date
    return mealPlans.find(p => {
      const normalizedPlanDate = normalizeDateString(p.date);
      if (!normalizedPlanDate) {
        if (p.date && p.date !== 'null' && p.date !== '') {
          console.warn(`Invalid date found in meal plan ${p._id}:`, p.date);
        }
        return false;
      }
      return normalizedPlanDate === date;
    });
  };

  const getMealsForDate = (date) => {
    // Find all meal plans for this date
    const plansForDate = mealPlans.filter(p => {
      const normalizedPlanDate = normalizeDateString(p.date);
      if (!normalizedPlanDate) {
        if (p.date && p.date !== 'null' && p.date !== '') {
          console.warn(`Invalid date found in meal plan ${p._id}:`, p.date);
        }
        return false;
      }
      return normalizedPlanDate === date;
    });

    let allMeals = [];

    plansForDate.forEach(plan => {
      // Handle meals array format (NEW FORMAT - populated by backend)
      if (plan.meals && Array.isArray(plan.meals)) {
        plan.meals.forEach(mealItem => {
          // Backend populates meals.meal with full meal object via .populate('meals.meal')
          if (mealItem.meal && mealItem.meal.name) {
            allMeals.push({
              mealType: mealItem.mealType,
              meal: mealItem.meal, // This is the populated meal object
              completed: mealItem.completed || false
            });
          } else {
            // Fallback: meal data failed to populate (invalid ID or deleted meal)
            // Create a placeholder meal so the UI doesn't break
            allMeals.push({
              mealType: mealItem.mealType,
              meal: {
                name: 'Meal Unavailable',
                calories: 0,
                category: ['Unknown'],
                description: 'This meal is no longer available'
              },
              completed: mealItem.completed || false
            });
          }
        });
      }

      // Handle breakfast/lunch/dinner/snack arrays format (backend format)
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (plan[mealType] && Array.isArray(plan[mealType])) {
          plan[mealType].forEach(meal => {
            allMeals.push({
              mealType: mealType,
              meal: meal,
              completed: false
            });
          });
        }
      });
    });

    console.log(`Meals for date ${date}:`, allMeals);
    return allMeals;
  };

  // Delete meal plan functionality
  const handleLongPressDay = (date) => {
    const meals = getMealsForDate(date);
    if (meals.length > 0) {
      setSelectedDateForDelete(date);
      setShowDeleteModal(true);
    }
  };

  const handleDeleteMealPlan = async () => {
    if (!selectedDateForDelete) return;

    try {
      setDeletingDate(selectedDateForDelete);
      setShowDeleteModal(false);

      console.log('🗑️ Starting delete process for date:', selectedDateForDelete);

      // Store the meal plan for undo functionality
      const mealsToDelete = getMealsForDate(selectedDateForDelete);
      setDeletedMealPlan({
        date: selectedDateForDelete,
        meals: mealsToDelete
      });

      console.log('🗑️ Calling mealPlansAPI.deleteMealPlan with date:', selectedDateForDelete);

      // Delete from backend
      const response = await mealPlansAPI.deleteMealPlan(selectedDateForDelete);
      console.log('🗑️ Delete API response:', response);

      // Remove from local state
      setMealPlans(prev => {
        const filteredPlans = prev.filter(plan => {
          const planDate = plan.date.includes('T') ? plan.date.split('T')[0] : plan.date;
          const shouldKeep = planDate !== selectedDateForDelete;
          console.log(`🗑️ Plan date: ${planDate}, Selected: ${selectedDateForDelete}, Keep: ${shouldKeep}`);
          return shouldKeep;
        });
        console.log('🗑️ Filtered meal plans count:', filteredPlans.length);
        return filteredPlans;
      });

      console.log('🗑️ Delete successful, showing undo snackbar');

      // Show undo snackbar
      setShowUndoSnackbar(true);
      setTimeout(() => {
        setShowUndoSnackbar(false);
        setDeletedMealPlan(null);
      }, 5000);

    } catch (error) {
      console.error('🗑️ Error deleting meal plan:', error);
      console.error('🗑️ Error details:', error.response?.data);
      Alert.alert('Error', `Failed to delete meal plan: ${error.response?.data?.message || error.message}`);
    } finally {
      setDeletingDate(null);
      setSelectedDateForDelete(null);
    }
  };

  const handleUndoDelete = async () => {
    if (!deletedMealPlan) return;

    try {
      setShowUndoSnackbar(false);

      // Here you would need to recreate the meal plan
      // For now, just reload the meal plans
      await loadMealPlans();

      Alert.alert('Info', 'Meal plan deletion undone. Please note: You may need to recreate the meal plan manually.');

    } catch (error) {
      console.error('Error undoing delete:', error);
      Alert.alert('Error', 'Failed to undo deletion.');
    } finally {
      setDeletedMealPlan(null);
    }
  };

  const handleFavoriteDay = async (date, meals) => {
    try {
      if (meals.length === 0) return;

      // Calculate plan details
      const totalMeals = meals.length;
      const totalCalories = meals.reduce((sum, meal) => sum + (meal.meal?.calories || 0), 0);
      const mealTypes = meals.map(meal => meal.mealType).filter((type, index, arr) => arr.indexOf(type) === index);

      // Create a unique plan name
      const planName = `Meal Plan for ${new Date(date).toLocaleDateString()}`;

      // First, create a meal plan in the database to get a proper ObjectId
      const mealPlanData = {
        name: planName,
        startDate: date,
        endDate: date,
        dietaryPreference: 'all',
        meals: meals.map(mealItem => ({
          date: date,
          mealType: mealItem.mealType,
          mealData: {
            name: mealItem.meal?.name || 'Unknown Meal',
            instanceId: mealItem.meal?._id || mealItem.meal?.id,
            calories: mealItem.meal?.calories || 0,
            category: mealItem.meal?.category || ['Unknown'],
            description: mealItem.meal?.description || ''
          }
        })),
        mealTimes: {
          breakfast: '08:00',
          lunch: '12:00',
          dinner: '18:00'
        }
      };

      console.log('Creating meal plan for favorites:', mealPlanData);

      // Save the meal plan to get an ObjectId
      const savedPlanResponse = await mealPlansAPI.saveMealPlan(mealPlanData);
      console.log('Saved meal plan response:', savedPlanResponse);

      // Extract the plan ID from the response
      // The response contains savedPlans array, get the first one's ID
      const planId = savedPlanResponse.data?.savedPlans?.[0]?._id ||
                   savedPlanResponse.data?.mealPlan?._id ||
                   savedPlanResponse.data?._id;

      if (!planId) {
        console.error('No plan ID found in response:', savedPlanResponse.data);
        throw new Error('Failed to create meal plan - no ID returned');
      }

      console.log('Using plan ID for favorites:', planId);

      // Now add to favorites with the proper ObjectId
      await addFavoriteMealPlan({
        mealPlanId: planId, // Use the actual ObjectId from the saved plan
        name: planName,
        date: date,
        totalCalories,
        totalMeals,
        mealTypes
      });

      Alert.alert('Success', 'Meal plan added to favorites!');
    } catch (error) {
      console.error('Error adding day to favorites:', error);
      Alert.alert('Error', 'Failed to add meal plan to favorites');
    }
  };

  const renderDayCard = (day) => {
    const meals = getMealsForDate(day.date);
    const mealPlan = getMealPlanForDate(day.date);
    const totalCalories = meals.reduce((sum, meal) => sum + (meal.meal?.calories || 0), 0);
    const isDeleting = deletingDate === day.date;
    const hasMeals = meals.length > 0;

    // Debug logging for rice bowls
    if (mealPlan && mealPlan.riceBowls > 0) {
      console.log(`Day ${day.date} has ${mealPlan.riceBowls} rice bowls`);
    }

    return (
      <TouchableOpacity
        key={day.date}
        style={[
          styles.dayCard,
          day.isToday && styles.todayCard,
          isDeleting && styles.dayCardDeleting
        ]}
        onLongPress={() => handleLongPressDay(day.date)}
        delayLongPress={500}
        disabled={isDeleting}
      >
        {/* Delete indicator */}
        {isDeleting && (
          <View style={styles.deletingOverlay}>
            <Ionicons name="trash" size={24} color={colors.surface} />
            <Text style={styles.deletingText}>Deleting...</Text>
          </View>
        )}

        <View style={styles.dayHeader}>
          <Text style={[styles.dayName, day.isToday && styles.todayText]}>
            {day.dayName}
          </Text>
          <Text style={[styles.dayNumber, day.isToday && styles.todayText]}>
            {day.dayNumber}
          </Text>
          {totalCalories > 0 && (
            <Text style={styles.caloriesText}>{totalCalories} cal</Text>
          )}
          {/* Show rice bowls if any */}
          {mealPlan && mealPlan.riceBowls > 0 && (
            <Text style={styles.riceBowlsText}>
              🍚 {mealPlan.riceBowls} bowl{mealPlan.riceBowls !== 1 ? 's' : ''}
            </Text>
          )}
          {/* Action buttons for days with meals */}
          {hasMeals && !isDeleting && (
            <View style={styles.dayActionButtons}>
              <TouchableOpacity
                style={styles.favoriteButton}
                onPress={() => handleFavoriteDay(day.date, meals)}
              >
                <Ionicons name="heart-outline" size={16} color={colors.secondary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickDeleteButton}
                onPress={() => handleLongPressDay(day.date)}
              >
                <Ionicons name="trash-outline" size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.mealsContainer}>
          {['breakfast', 'lunch', 'dinner', 'snack'].map(mealType => {
            const meal = meals.find(m => m.mealType?.toLowerCase() === mealType);
            return (
              <TouchableOpacity
                key={mealType}
                style={[
                  styles.mealSlot,
                  meal && styles.mealSlotFilled
                ]}
                onPress={() => navigation.navigate('CreateMealPlan', {
                  selectedDate: day.date,
                  mealType,
                  existingMeal: meal,
                  allMealsForDate: meals, // Pass all meals for this date
                })}
                disabled={isDeleting}
              >
                <Text style={styles.mealTypeText}>
                  {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                </Text>
                {meal ? (
                  <View style={styles.mealCardContent}>
                    <Text style={styles.mealNameText} numberOfLines={1}>
                      {meal.meal?.name || 'Unknown Meal'}
                    </Text>
                    <View style={styles.mealMetaInfo}>
                      {meal.meal?.category && (
                        <View style={styles.mealCategoryTag}>
                          <Text style={styles.mealCategoryText}>
                            {Array.isArray(meal.meal.category) ? String(meal.meal.category[0]) : String(meal.meal.category)}
                          </Text>
                        </View>
                      )}
                      {meal.meal?.calories && (
                        <Text style={styles.mealCaloriesText}>
                          {meal.meal.calories} cal
                        </Text>
                      )}
                    </View>
                    {meal.meal?.description && (
                      <Text style={styles.mealDescriptionText} numberOfLines={1}>
                        {meal.meal.description}
                      </Text>
                    )}
                  </View>
                ) : (
                  <View style={styles.addMealButton}>
                    <Ionicons name="add" size={16} color={colors.primary} />
                    <Text style={styles.addMealText}>Add</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </TouchableOpacity>
    );
  };

  const handleGenerateMealPlan = () => {
    setShowGenerateModal(true);
  };

  const handleConfirmGenerate = async () => {
    try {
      setLoading(true);
      setShowGenerateModal(false);

      // Calculate end date based on number of days
      const start = new Date(startDate);
      const end = new Date(start);
      end.setDate(start.getDate() + (numberOfDays - 1));

      const endDateString = end.toISOString().split('T')[0];

      console.log('Generating meal plan for:', {
        startDate,
        endDate: endDateString,
        numberOfDays
      });

      const response = await mealPlansAPI.generateMealPlan({
        startDate,
        endDate: endDateString,
        includeFamily: true,
        calorieTarget: user?.dietaryPreferences?.calorieTarget || 2000
      });

      console.log('Generate meal plan response:', response);

      if (response.data && response.data.success) {
        // Reload meal plans to show the generated ones
        await loadMealPlans();

        Alert.alert(
          'Success!',
          `Generated ${response.data.generatedPlans} meal plans for ${numberOfDays} day(s) starting from ${new Date(startDate).toLocaleDateString()} based on your preferences.`,
          [{ text: 'OK' }]
        );
      } else {
        throw new Error('Failed to generate meal plan');
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      Alert.alert(
        'Error',
        `Failed to generate meal plan: ${error.response?.data?.message || error.message}`,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFavoritePlan = async (plan) => {
    try {
      const planId = plan._id || plan.id;
      const isCurrentlyFavorite = isFavoriteMealPlan(planId);

      if (isCurrentlyFavorite) {
        await removeFavoriteMealPlan(planId);
      } else {
        // Calculate plan details for favorites
        const totalMeals = plan.meals?.length || 0;
        const totalCalories = plan.meals?.reduce((sum, meal) => sum + (meal.meal?.calories || 0), 0) || 0;
        const mealTypes = plan.meals?.map(meal => meal.mealType).filter((type, index, arr) => arr.indexOf(type) === index) || [];

        await addFavoriteMealPlan({
          mealPlanId: planId,
          name: plan.name || 'Meal Plan',
          date: plan.date || new Date().toISOString().split('T')[0],
          totalCalories,
          totalMeals,
          mealTypes
        });
      }
    } catch (error) {
      console.error('Error toggling favorite meal plan:', error);
      Alert.alert('Error', 'Failed to update favorite status');
    }
  };

  const renderSavedPlan = ({ item: plan }) => {
    const planId = plan._id || plan.id;
    const isCurrentlyFavorite = isFavoriteMealPlan(planId);

    return (
      <TouchableOpacity style={styles.savedPlanCard}>
        <View style={styles.savedPlanHeader}>
          <Text style={styles.savedPlanName}>{plan.name}</Text>
          <TouchableOpacity
            style={styles.favoritePlanButton}
            onPress={() => handleToggleFavoritePlan(plan)}
          >
            <Ionicons
              name={isCurrentlyFavorite ? "heart" : "heart-outline"}
              size={20}
              color={isCurrentlyFavorite ? colors.secondary : colors.textSecondary}
            />
          </TouchableOpacity>
        </View>
      <Text style={styles.savedPlanDate}>
        Created: {new Date(plan.createdAt).toLocaleDateString()}
      </Text>
      <Text style={styles.savedPlanDescription}>
        {plan.description || `${plan.meals?.length || 0} meals planned`}
      </Text>
      <View style={styles.savedPlanActions}>
        <TouchableOpacity style={styles.useTemplateButton}>
          <Text style={styles.useTemplateText}>Use Template</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.favoritePlanActionButton}
          onPress={() => handleToggleFavoritePlan(plan)}
        >
          <Ionicons
            name={isCurrentlyFavorite ? "heart" : "heart-outline"}
            size={16}
            color={colors.primary}
          />
          <Text style={styles.favoritePlanActionText}>
            {isCurrentlyFavorite ? 'Unfavorite' : 'Favorite'}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
    );
  };

  const renderWeeklyView = () => (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]}
        />
      }
    >
      {/* Week Navigation */}
      <View style={styles.weekNavigation}>
        <TouchableOpacity
          style={styles.weekNavButton}
          onPress={() => navigateWeek(-1)}
        >
          <Ionicons name="chevron-back" size={24} color={colors.primary} />
        </TouchableOpacity>

        <View style={styles.weekInfo}>
          <Text style={styles.weekText}>
            {new Date(selectedWeek.start).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            })} - {new Date(selectedWeek.end).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            })}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.weekNavButton}
          onPress={() => navigateWeek(1)}
        >
          <Ionicons name="chevron-forward" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Days Grid */}
      <View style={styles.daysGrid}>
        {getDaysOfWeek().map(renderDayCard)}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('CreateMealPlan', {
            selectedDate: new Date().toISOString().split('T')[0],
          })}
        >
          <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Add Meal</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleGenerateMealPlan}
        >
          <Ionicons name="bulb-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Generate Plan</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleSetPreferences}
        >
          <Ionicons name="settings-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>{getPreferencesButtonText()}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.quickActionButton}>
          <Ionicons name="copy-outline" size={24} color={colors.primary} />
          <Text style={styles.quickActionText}>Copy Week</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderSavedView = () => (
    <View style={styles.savedContainer}>
      <FlatList
        data={savedPlans}
        renderItem={renderSavedPlan}
        keyExtractor={(item) => item.id || item._id}
        contentContainerStyle={styles.savedList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={commonStyles.emptyContainer}>
            <Ionicons name="bookmark-outline" size={64} color={colors.textSecondary} />
            <Text style={commonStyles.emptyTitle}>No saved meal plans</Text>
            <Text style={commonStyles.emptySubtitle}>
              Create and save meal plan templates for quick reuse
            </Text>
          </View>
        }
      />
    </View>
  );

  // Delete confirmation modal
  const renderDeleteModal = () => {
    if (!selectedDateForDelete) return null;

    const meals = getMealsForDate(selectedDateForDelete);
    const totalCalories = meals.reduce((sum, meal) => sum + (meal.meal?.calories || 0), 0);
    const mealCount = meals.length;

    return (
      <Modal
        visible={showDeleteModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.deleteModalContainer}>
            <View style={styles.deleteModalHeader}>
              <Ionicons name="warning" size={32} color={colors.secondary} />
              <Text style={styles.deleteModalTitle}>Delete Meal Plan</Text>
            </View>

            <View style={styles.deleteModalContent}>
              <Text style={styles.deleteModalDate}>
                {new Date(selectedDateForDelete).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>

              <View style={styles.deleteModalStats}>
                <View style={styles.deleteModalStat}>
                  <Text style={styles.deleteModalStatNumber}>{mealCount}</Text>
                  <Text style={styles.deleteModalStatLabel}>Meals</Text>
                </View>
                <View style={styles.deleteModalStat}>
                  <Text style={styles.deleteModalStatNumber}>{totalCalories}</Text>
                  <Text style={styles.deleteModalStatLabel}>Calories</Text>
                </View>
              </View>

              <Text style={styles.deleteModalWarning}>
                This action will permanently delete all meals planned for this date. This cannot be undone.
              </Text>
            </View>

            <View style={styles.deleteModalActions}>
              <TouchableOpacity
                style={styles.deleteModalCancelButton}
                onPress={() => setShowDeleteModal(false)}
              >
                <Text style={styles.deleteModalCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.deleteModalDeleteButton}
                onPress={handleDeleteMealPlan}
              >
                <Ionicons name="trash" size={16} color={colors.surface} />
                <Text style={styles.deleteModalDeleteText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  // Undo snackbar
  const renderUndoSnackbar = () => {
    if (!showUndoSnackbar || !deletedMealPlan) return null;

    return (
      <View style={styles.undoSnackbar}>
        <View style={styles.undoSnackbarContent}>
          <Ionicons name="checkmark-circle" size={20} color={colors.surface} />
          <Text style={styles.undoSnackbarText}>
            Meal plan for {new Date(deletedMealPlan.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} deleted
          </Text>
        </View>
        <TouchableOpacity
          style={styles.undoButton}
          onPress={handleUndoDelete}
        >
          <Text style={styles.undoButtonText}>UNDO</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Meal Plans</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('CreateMealPlan')}
        >
          <Ionicons name="add" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'weekly' && styles.tabButtonActive]}
          onPress={() => setActiveTab('weekly')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'weekly' && styles.tabButtonTextActive
          ]}>
            Weekly View
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'saved' && styles.tabButtonActive]}
          onPress={() => setActiveTab('saved')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'saved' && styles.tabButtonTextActive
          ]}>
            Saved Plans
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {loading ? (
        <View style={commonStyles.loadingContainer}>
          <Text style={commonStyles.loadingText}>Loading meal plans...</Text>
        </View>
      ) : (
        <>
          {activeTab === 'weekly' && renderWeeklyView()}
          {activeTab === 'saved' && renderSavedView()}
        </>
      )}

      {/* Delete Modal */}
      {renderDeleteModal()}

      {/* Undo Snackbar */}
      {renderUndoSnackbar()}

      {/* Generate Meal Plan Modal */}
      <Modal
        visible={showGenerateModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGenerateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Generate Meal Plan</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowGenerateModal(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Number of Days</Text>
                <View style={styles.daysSelector}>
                  {[1, 3, 5, 7, 14].map(days => (
                    <TouchableOpacity
                      key={days}
                      style={[
                        styles.dayOption,
                        numberOfDays === days && styles.dayOptionSelected
                      ]}
                      onPress={() => setNumberOfDays(days)}
                    >
                      <Text style={[
                        styles.dayOptionText,
                        numberOfDays === days && styles.dayOptionTextSelected
                      ]}>
                        {days} {days === 1 ? 'Day' : 'Days'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Start Date</Text>
                <TouchableOpacity
                  style={styles.datePickerButton}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={styles.datePickerText}>
                    {new Date(startDate).toLocaleDateString('en-US', {
                      weekday: 'short',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </Text>
                  <Ionicons name="calendar-outline" size={20} color={colors.primary} />
                </TouchableOpacity>
                <Text style={styles.dateHelper}>
                  End Date: {(() => {
                    const start = new Date(startDate);
                    const end = new Date(start);
                    end.setDate(start.getDate() + (numberOfDays - 1));
                    return end.toLocaleDateString('en-US', {
                      weekday: 'short',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    });
                  })()}
                </Text>
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowGenerateModal(false)}
              >
                <Text style={styles.modalCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalConfirmButton}
                onPress={handleConfirmGenerate}
              >
                <Text style={styles.modalConfirmText}>Generate Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display="default"
          onChange={(event, date) => {
            setShowDatePicker(false);
            if (date) {
              // Use local date to avoid timezone issues
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const formattedDate = `${year}-${month}-${day}`;
              setSelectedDate(date);
              setStartDate(formattedDate);
            }
          }}
          minimumDate={new Date()}
        />
      )}

      {/* Floating Chat Button */}
      <FloatingChatButton />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerButton: {
    padding: spacing.sm,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonActive: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: colors.primary,
  },
  weekNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  weekNavButton: {
    padding: spacing.sm,
  },
  weekInfo: {
    flex: 1,
    alignItems: 'center',
  },
  weekText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  daysGrid: {
    padding: spacing.md,
  },
  dayCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  todayCard: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  dayName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  dayNumber: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  todayText: {
    color: colors.primary,
  },
  caloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  mealsContainer: {
    gap: spacing.sm,
  },
  mealSlot: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.small,
    padding: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
  },
  mealSlotFilled: {
    backgroundColor: colors.surface,
    borderStyle: 'solid',
    borderColor: colors.primary,
  },
  mealTypeText: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  mealNameText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  mealCardContent: {
    flex: 1,
  },
  mealMetaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },
  mealCategoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.xs,
  },
  mealCategoryText: {
    color: colors.surface,
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
  },
  mealCaloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  mealDescriptionText: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 12,
  },
  addMealButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addMealText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    backgroundColor: colors.surface,
    marginTop: spacing.md,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: spacing.md,
  },
  quickActionText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginTop: spacing.xs,
    fontWeight: '500',
  },
  savedContainer: {
    flex: 1,
  },
  savedList: {
    padding: spacing.md,
  },
  savedPlanCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  savedPlanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  savedPlanName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
  },
  savedPlanDate: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  savedPlanDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  savedPlanActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  useTemplateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.small,
  },
  useTemplateText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '500',
  },
  favoritePlanButton: {
    padding: spacing.sm,
  },
  favoritePlanActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  favoritePlanActionText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },

  // Delete functionality styles
  dayCardDeleting: {
    opacity: 0.6,
    backgroundColor: colors.error + '20',
  },
  deletingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.error + 'CC',
    borderRadius: borderRadius.medium,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  deletingText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    marginTop: spacing.xs,
  },
  dayActionButtons: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  favoriteButton: {
    padding: spacing.xs,
    borderRadius: borderRadius.small,
    backgroundColor: colors.background,
  },
  quickDeleteButton: {
    padding: spacing.xs,
    borderRadius: borderRadius.small,
    backgroundColor: colors.background,
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  deleteModalContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.large,
    padding: spacing.lg,
    width: '100%',
    maxWidth: 400,
    ...commonStyles.shadowLarge,
  },
  deleteModalHeader: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  deleteModalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: spacing.sm,
  },
  deleteModalContent: {
    marginBottom: spacing.lg,
  },
  deleteModalDate: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  deleteModalStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
  },
  deleteModalStat: {
    alignItems: 'center',
  },
  deleteModalStatNumber: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.primary,
  },
  deleteModalStatLabel: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  deleteModalWarning: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  deleteModalActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  deleteModalCancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  deleteModalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  deleteModalDeleteButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.error,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xs,
  },
  deleteModalDeleteText: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '500',
  },

  // Undo snackbar styles
  undoSnackbar: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.md,
    right: spacing.md,
    backgroundColor: colors.text,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...commonStyles.shadowLarge,
  },
  undoSnackbarContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  undoSnackbarText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    marginLeft: spacing.sm,
    flex: 1,
  },
  undoButton: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
  },
  undoButtonText: {
    color: colors.primary,
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
  },
  riceBowlsText: {
    fontSize: fonts.sizes.tiny,
    color: colors.textSecondary,
    marginTop: 2,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.large,
    width: '100%',
    maxWidth: 400,
    ...commonStyles.shadowLarge,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: spacing.xs,
  },
  modalBody: {
    padding: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  daysSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  dayOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
  },
  dayOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  dayOptionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '500',
  },
  dayOptionTextSelected: {
    color: colors.surface,
  },
  datePickerButton: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    backgroundColor: colors.background,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  datePickerText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
  },
  dateHelper: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: spacing.md,
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  modalConfirmButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.primary,
    alignItems: 'center',
  },
  modalConfirmText: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
  },
});

export default MealPlansScreen;
